<view class="chain_index">
  <immersive-navbar
    id="navbar"
    title="企业"
    show-back-btn="{{false}}"
    bg-color="#E72410"
    text-color="#ffffff"
    max-scroll-distance="{{200}}"
    show-loading-footer="{{false}}"
  >
    <view slot="content" class="content">
      <view class="h_head">
        <image src="/image/report/e_enter_bg.png"></image>
        <view class="search_wrap">
          <!-- 假的input框 ，因为i要跳转主要是做样式 -->
          <view
            hover-class="none"
            class="h_head_input"
            bindtap="handleClick"
            data-type="search"
          >
            <view class="h_search">
              <image
                src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/search.png"
              ></image>
            </view>
            <view class="texts text-ellipsis">输入企业关键词</view>
          </view>
          <!-- 筛选 -->
          <view class="filter">多维搜索</view>
        </view>
      </view>

      <view hidden="{{!isLogin}}" class="main-content">
        <!-- 企业功能网格 -->
        <view class="zt-container">
          <view class="zt-grid">
            <view
              wx:for="{{enterpriseData}}"
              wx:key="index"
              class="zt-item"
              bindtap="onEnterpriseClick"
              data-item="{{item}}"
            >
              <image class="zt-image" src="{{item.icon}}" mode="aspectFill" />
              <view class="zt-name">{{item.name}}</view>
            </view>
          </view>
        </view>

        <!-- 重点企业模块 -->
        <view class="enterprise-section">
          <view class="enterprise-card">
            <!-- 标题 -->
            <view class="card-title">
              <text>重点企业</text>
            </view>
            <!-- 内容 -->
            <view class="enterprise-content">
              <view class="enterprise-wrap">
                <view
                  class="enterprise-item"
                  wx:for="{{industryData}}"
                  wx:key="index"
                  bindtap="onEnterpriseItemClick"
                  data-item="{{item}}"
                  data-index="{{index}}"
                  hover-class="enterprise-item-active"
                  hover-stay-time="200"
                  hover-start-time="0"
                >
                  <!-- 企业类型名称 -->
                  <view class="enterprise-item-name">{{item.name}}</view>
                  <!-- 企业数量 -->
                  <view class="enterprise-item-count">
                    {{item.count}} <text class="enterprise-item-unit">家</text>
                  </view>
                  <!-- 背景装饰图片 -->
                  <image
                    class="enterprise-item-bg"
                    src="{{item.img}}"
                    mode="aspectFit"
                  ></image>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 集团模块 -->
        <view class="enterprise-section">
          <view class="enterprise-card">
            <!-- 标题 -->
            <view class="card-title">
              <text>集团</text>
            </view>
            <!-- 内容 -->
            <view class="enterprise-content">
              <view class="group-wrap">
                <view
                  class="group-item"
                  wx:for="{{groupData}}"
                  wx:key="index"
                  bindtap="onGroupItemClick"
                  data-item="{{item}}"
                  data-index="{{index}}"
                  hover-class="group-item-active"
                  hover-stay-time="200"
                  hover-start-time="0"
                >
                  <!-- 集团名称 -->
                  <view class="group-item-name">{{item.name}}</view>
                  <!-- 集团数量 -->
                  <view class="group-item-count">{{item.count}}家</view>
                  <!-- 背景装饰图片 -->
                  <image
                    class="group-item-bg"
                    src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent1.png"
                    mode="aspectFit"
                  ></image>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 资本模块 -->
        <view class="enterprise-section">
          <view class="enterprise-card">
            <!-- 标题 -->
            <view class="card-title">
              <text>资本</text>
            </view>
            <!-- 内容 -->
            <view class="enterprise-content">
              <view class="capital-wrap">
                <!-- 左边大卡片 -->
                <view
                  class="capital-left-item"
                  bindtap="onCapitalItemClick"
                  data-item="{{capitalData[0]}}"
                  data-index="0"
                  hover-class="capital-item-active"
                  hover-stay-time="200"
                  hover-start-time="0"
                >
                  <!-- 主标题 -->
                  <view
                    class="capital-left-title"
                    >{{capitalData[0].name}}</view
                  >
                  <!-- 副标题 -->
                  <view
                    class="capital-left-subtitle"
                    >{{capitalData[0].subtitle}}</view
                  >
                  <!-- 右下角装饰图片 -->
                  <image
                    class="capital-left-bg"
                    src="{{capitalData[0].img}}"
                    mode="aspectFit"
                  ></image>
                </view>

                <!-- 右边两个小卡片 -->
                <view class="capital-right-container">
                  <!-- 第一个小卡片 -->
                  <view
                    class="capital-right-item capital-right-item-1"
                    bindtap="onCapitalItemClick"
                    data-item="{{capitalData[1]}}"
                    data-index="1"
                    hover-class="capital-item-active"
                    hover-stay-time="200"
                    hover-start-time="0"
                  >
                    <!-- 标题 -->
                    <view
                      class="capital-right-title capital-right-title-1"
                      >{{capitalData[1].name}}</view
                    >
                    <!-- 右下角图片 -->
                    <image
                      class="capital-right-bg"
                      src="{{capitalData[1].img}}"
                      mode="aspectFit"
                    ></image>
                  </view>

                  <!-- 第二个小卡片 -->
                  <view
                    class="capital-right-item capital-right-item-2"
                    bindtap="onCapitalItemClick"
                    data-item="{{capitalData[2]}}"
                    data-index="2"
                    hover-class="capital-item-active"
                    hover-stay-time="200"
                    hover-start-time="0"
                  >
                    <!-- 标题 -->
                    <view
                      class="capital-right-title capital-right-title-2"
                      >{{capitalData[2].name}}</view
                    >
                    <!-- 右下角图片 -->
                    <image
                      class="capital-right-bg"
                      src="{{capitalData[2].img}}"
                      mode="aspectFit"
                    ></image>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <view style="height: 40rpx;"></view>
      </view>
      <view class="card-login" hidden="{{isLogin}}">
        <image
          src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/quesheng.png"
          mode="aspectFit"
        ></image>
        <view class="txt">未登录的用户暂时无法使用项目功能</view>
        <LoginBtn isCard></LoginBtn>
      </view>
    </view>
  </immersive-navbar>
</view>

const app = getApp();

Page({
  data: {
    isLogin: app.isLogin(), // 是否登录
    // 企业功能数据
    enterpriseData: [
      {
        id: 1,
        name: '企业猎搜',
        icon: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/enterprise/hunt.png',
        url: '/companyPackage/pages/searTerm/sear-term'
      },
      {
        id: 2,
        name: '产业链',
        icon: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/enterprise/chain.png',
        url: '/pages/hIndusty/index'
      },
      {
        id: 3,
        name: '地图招商',
        icon: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/enterprise/map.png',
        url: '/companyPackage/pages/merchantsMap/merchants'
      },
      {
        id: 4,
        name: '权威榜单',
        icon: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/enterprise/rank.png',
        url: '/companyPackage/pages/authoritativeList/authoritativeList'
      },
      {
        id: 5,
        name: '风险监控',
        icon: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/enterprise/risk.png',
        url: '/pages/companyInfo/companyInfo'
      },
      {
        id: 6,
        name: '融资事件',
        icon: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/enterprise/finance.png',
        url: '/subPackage/pages/financing/home/<USER>'
      },
      {
        id: 7,
        name: '找关系',
        icon: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/enterprise/relation.png',
        url: '/companyPackage/pages/mineRelation/relation'
      },
      {
        id: 8,
        name: '查园区',
        icon: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/enterprise/park.png',
        url: '/subPackage/pages/searchPark/index'
      }
    ],
    // 重点企业数据（模拟数据）
    industryData: [
      {
        name: '上市企业',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent1.png',
        count: 1256,
        key: 'listed_ent_cnt'
      },
      {
        name: '小巨人',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent2.png',
        count: 3428,
        key: 'little_giant'
      },
      {
        name: '单项冠军',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent3.png',
        count: 892,
        key: 'single_champ'
      },
      {
        name: '高新技术企业',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent4.png',
        count: 15672,
        key: 'high_tech_ent_cnt'
      },
      {
        name: '专精特新企业',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent5.png',
        count: 8934,
        key: 'specialized_ent_cnt'
      },
      {
        name: '创新型中小企业',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent6.png',
        count: 12456
      },
      {
        name: '科技型企业',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent7.png',
        count: 6789,
        key: 'technology_ent_cnt'
      }
    ],
    // 集团数据
    groupData: [
      {
        name: '央企集团',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent1.png',
        count: 156,
        key: 'central_group'
      },
      {
        name: '国企集团',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent2.png',
        count: 892,
        key: 'state_group'
      },
      {
        name: '民企集团',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent3.png',
        count: 2456,
        key: 'private_group'
      }
    ],
    // 资本数据
    capitalData: [
      {
        name: 'VC机构',
        subtitle: '风险投资机构',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent1.png',
        count: 1567,
        key: 'vc_capital'
      },
      {
        name: 'PE机构',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent2.png',
        count: 892,
        key: 'pe_capital'
      },
      {
        name: '产业基金',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent3.png',
        count: 456,
        key: 'industry_fund'
      }
    ]
  },
  onShow: function () {
    this.setData({
      isLogin: app.isLogin()
    });
  },

  // 处理搜索点击事件
  async handleClick(e) {
    const {isLogin} = this.data;
    const constant = e.currentTarget.dataset?.type;

    if (!isLogin) {
      app.route(this, '/pages/login/login');
      return;
    }

    if (constant === 'search') {
      // 跳转到企业搜索页面
      app.route(this, '/companyPackage/pages/searTerm/sear-term');
    }
  },

  // 处理企业功能点击事件
  async onEnterpriseClick(e) {
    const {isLogin} = this.data;
    const {item} = e.currentTarget.dataset;

    if (!isLogin) {
      app.route(this, '/pages/login/login');
      return;
    }
    // 跳转到对应页面
    app.route(this, item.url);
  },

  // 处理重点企业点击事件
  async onEnterpriseItemClick(e) {
    const {isLogin} = this.data;
    const {item} = e.detail;

    if (!isLogin) {
      app.route(this, '/pages/login/login');
      return;
    }
    // 跳转到企业列表页面
    const url = `/companyPackage/pages/searTerm/sear-term?type=${
      item.key
    }&title=${encodeURIComponent(item.name)}`;
    app.route(this, url);
  },

  // 处理集团点击事件
  async onGroupItemClick(e) {
    const {isLogin} = this.data;
    const {item} = e.detail;

    if (!isLogin) {
      app.route(this, '/pages/login/login');
      return;
    }

    const url = `/companyPackage/pages/searTerm/sear-term?type=${
      item.key
    }&title=${encodeURIComponent(item.name)}`;
    app.route(this, url);
  },

  // 处理资本点击事件
  async onCapitalItemClick(e) {
    const {isLogin} = this.data;
    const {item} = e.detail;

    if (!isLogin) {
      app.route(this, '/pages/login/login');
      return;
    }
    // 跳转到资本搜索页面
    const url = `/companyPackage/pages/searTerm/sear-term?type=${
      item.key
    }&title=${encodeURIComponent(item.name)}`;
    app.route(this, url);
  }
});
